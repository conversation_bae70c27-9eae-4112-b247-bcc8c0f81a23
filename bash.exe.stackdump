Stack trace:
Frame         Function      Args
0007FFFF9DD0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF8CD0) msys-2.0.dll+0x2118E
0007FFFF9DD0  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFA0A8) msys-2.0.dll+0x69BA
0007FFFF9DD0  0002100469F2 (00021028DF99, 0007FFFF9C88, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9DD0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9DD0  00021006A545 (0007FFFF9DE0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFA0B0  00021006B9A5 (0007FFFF9DE0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFBF0140000 ntdll.dll
7FFBEF930000 KERNEL32.DLL
7FFBED860000 KERNELBASE.dll
7FFBEE200000 USER32.dll
7FFBED830000 win32u.dll
000210040000 msys-2.0.dll
7FFBEE5F0000 GDI32.dll
7FFBED330000 gdi32full.dll
7FFBEDC50000 msvcp_win.dll
7FFBEDD90000 ucrtbase.dll
7FFBEE430000 advapi32.dll
7FFBEE150000 msvcrt.dll
7FFBEFDD0000 sechost.dll
7FFBEDEE0000 RPCRT4.dll
7FFBEC870000 CRYPTBASE.DLL
7FFBED290000 bcryptPrimitives.dll
7FFBEFA00000 IMM32.DLL
