Stack trace:
Frame         Function      Args
0007FFFF8E80  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF7D80) msys-2.0.dll+0x2118E
0007FFFF8E80  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFF9158) msys-2.0.dll+0x69BA
0007FFFF8E80  0002100469F2 (00021028DF99, 0007FFFF8D38, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF8E80  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF8E80  00021006A545 (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFF9160  00021006B9A5 (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB73660000 ntdll.dll
7FFB71660000 KERNEL32.DLL
7FFB70F30000 KERNELBASE.dll
7FFB73350000 USER32.dll
7FFB713D0000 win32u.dll
7FFB721B0000 GDI32.dll
7FFB70DF0000 gdi32full.dll
7FFB71320000 msvcp_win.dll
7FFB70B70000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFB715A0000 advapi32.dll
7FFB732A0000 msvcrt.dll
7FFB714F0000 sechost.dll
7FFB71910000 RPCRT4.dll
7FFB6FDB0000 CRYPTBASE.DLL
7FFB70D50000 bcryptPrimitives.dll
7FFB72110000 IMM32.DLL
