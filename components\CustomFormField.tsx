'use client'
import React from 'react'
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Control, ControllerRenderProps, FieldValues, Path } from 'react-hook-form'
import { FormFieldType } from './Form/PatientForm'
import Image from 'next/image'
import 'react-phone-number-input/style.css'
import PhoneInput from 'react-phone-number-input'
import { useState } from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { Select, SelectContent, SelectTrigger, SelectValue } from './ui/select'

interface CustomProps<T extends FieldValues = FieldValues>{
    control: Control<T>,
    fieldType : FormFieldType,
    name : Path<T>,
    label?: string,
    placeholder?: string,
    iconSrc?: string,
    iconAlt?: string,
    disabled?: boolean,
    dateFormat?: string,
    showTimeSelect?: boolean,
    children?: React.ReactNode,
    renderSkeleton?: (field: ControllerRenderProps<T>) => React.ReactNode,
}


const RenderField = <T extends FieldValues>({ field, props }: { field: ControllerRenderProps<T>, props: CustomProps<T> }) => {
  const { fieldType, iconSrc, iconAlt, placeholder, disabled,showTimeSelect,dateFormat ,renderSkeleton} = props;

  switch (fieldType) {
    case FormFieldType.INPUT:
      return (
        <div className="flex w-full rounded-md border border-dark-500 bg-dark-400">
          {iconSrc && (
            <Image
              src={iconSrc}
              height={24}
              width={24}
              alt={iconAlt || "icon"}
              className="ml-2 my-auto flex-shrink-0"
            />
          )}
          <FormControl>
            <Input
              placeholder={placeholder}
              disabled={disabled}
              {...field}
              className="shad-input border-0 w-full"
            />
          </FormControl>
        </div>
      );

    case FormFieldType.TEXTAREA:
      return (
        <FormControl>
          <textarea
            placeholder={placeholder}
            disabled={disabled}
            {...field}
            className="shad-input w-full min-h-[120px] resize-none"
          />
        </FormControl>
      );

    case FormFieldType.CHECKBOX:
      return (
        <FormControl>
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              disabled={disabled}
              {...field}
              className="checkbox"
            />
            {props.children}
          </div>
        </FormControl>
      );

    case FormFieldType.PHONE_INPUT:
      return (
        <FormControl>
          <PhoneInput
            defaultCountry="TN"
            placeholder={placeholder}
            international
            withCountryCallingCode
            value={field.value as string | undefined}
            onChange={field.onChange}
            className="input-phone w-full"
          />
        </FormControl>
      );
    case FormFieldType.DATE_PICKER:
      return (
        <div className='flex rounded-md border border-dark-500 bg-dark-400'>
            <Image src="/assets/icons/calendar.svg" alt="calendar" height={24} width={24} className='ml-2 my-auto flex-shrink-0'/>
            <FormControl>
                    <DatePicker
                    selected={field.value}
                    onChange={(date: Date | null) => field.onChange(date)}
                     dateFormat={dateFormat ?? 'MM/dd/yyyy'}
                     showTimeSelect={showTimeSelect ?? false}
                     timeInputLabel="Time:"
                     wrapperClassName="date-picker"
                     className="w-full"
                     />
            </FormControl>
        </div>
      );

    case FormFieldType.SKELETON:
      return renderSkeleton ? renderSkeleton(field) : null;

    case FormFieldType.SELECT:
      return (
        <FormControl>
          <Select onValueChange={field.onChange} defaultValue={field.value}>
            <FormControl className="shad-select-trigger">
              <SelectTrigger>
                      <SelectValue placeholder={placeholder}/>    
              </SelectTrigger>
              
            </FormControl>
            <SelectContent className='shad-select-content'>
              {props.children}
            </SelectContent>
          </Select>
          {props.children}
        </FormControl>
      );

    default:
      return null;
  }
};


const CustomFormField = <T extends FieldValues>(props: CustomProps<T>) => {
  const { control, fieldType, name, label } = props;

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className="flex-1 w-full">
          {fieldType !== FormFieldType.CHECKBOX && label && (
            <FormLabel className="text-14-medium text-dark-700">{label}</FormLabel>
          )}
          <RenderField field={field} props={props}/>
          <FormMessage className="shad-error" />
        </FormItem>
      )}
    />
  );
};


export default CustomFormField
