"use client"



import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
 
import {
  Form,
  FormControl,
} from "@/components/ui/form"
import CustomFormField from "../CustomFormField"
import SubmitButton from "../SubmitButton"
import { useState } from "react"
import { RegisterFormValidation } from "@/lib/Validation"
import { useRouter } from "next/navigation"
import { createUser } from "@/lib/actions/patient.actions"
import { FormFieldType } from "./PatientForm"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Doctors, GenderOptions } from "@/constants"
import { SelectItem } from "../ui/select"
import Image from "next/image"


const  RegisterForm = ({user}:{user:User})=> {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  // 1. Define your form.
  const form = useForm<z.infer<typeof RegisterFormValidation>>({
    resolver: zod<PERSON>esolver(RegisterFormValidation),
    defaultValues: {
      username: "",
      email: "",
      phone: "",
      birthDate: undefined,
      Gender: undefined,
    },
  })

  // 2. Define a submit handler.
  async function onSubmit({username,email,phone,birthDate,Gender}: z.infer<typeof RegisterFormValidation>) {
    setIsLoading(true);
    try {
      const userData = {username,email,phone,birthDate,Gender}

      // Simulate API call
      console.log("Registration data:", userData);
      // TODO: Implement actual registration API call
      // const user = await registerUser(userData);
      // if(user) router.push(`/patients/${user.$id}/dashboard`)

    } catch (error) {
      console.error("Form submission error:", error);
    } finally {
      setIsLoading(false);
    }
  }
  return (
     <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-12 flex-1">
        <section className="space-y-4">
          <h1 className="header">Welcome👋</h1>
          <p className="text-dark-700">Let us know more about yourself.</p>
        </section>

        <section className="space-y-6">
          <div className="mb-9 space-y-1">
             <h2 className="sub-header">Personal information</h2>
          </div>
        </section>

        <CustomFormField
          fieldType={FormFieldType.INPUT}
          control={form.control}
          name="username"
          placeholder="John Doe"
          label="Full Name"
          iconSrc="/assets/icons/user.svg"
          iconAlt="user"
        />
        <div className="flex flex-col gap-6 xl:flex-row ">
          <CustomFormField
          fieldType={FormFieldType.INPUT}
          control={form.control}
          name="email"
          label="Email"
          placeholder="<EMAIL>"
          iconSrc="/assets/icons/email.svg"
          iconAlt="email"
        />

        <CustomFormField
          fieldType={FormFieldType.PHONE_INPUT}
          control={form.control}
          name="phone"
          label="Phone"
          placeholder="+216 12234456"
          iconSrc="/assets/icons/email.svg"
          iconAlt="email"
        />

        </div>

        <div className="flex flex-col gap-6 xl:flex-row ">
        <CustomFormField
          fieldType={FormFieldType.DATE_PICKER}
          control={form.control}
          name="birthDate"
          label="Date of Birth"
          placeholder="<EMAIL>"
          
        />

        <CustomFormField
          fieldType={FormFieldType.SKELETON}
          control={form.control}
          name="Gender"
          label="Gender"
          renderSkeleton={(field)=>(
            <FormControl>
              <RadioGroup
                className="flex h-11 gap-6 xl:justify-between"
                onValueChange={field.onChange}
                defaultValue={field.value as string}
              >
                {GenderOptions.map((option)=>(
                  <div key={option} className="radio-group">
                      <RadioGroupItem value={option} id={option} />
                      <Label htmlFor={option} className="cursor-pointer">
                        {option}
                      </Label>
                  </div>
                ))}
              </RadioGroup>
            </FormControl>
          )}
        />

        </div>

     

        <div className="flex flex-col gap-6 xl:flex-row ">

          <CustomFormField
          fieldType={FormFieldType.INPUT}
          control={form.control}
          name="address"
          placeholder="14 Street ,New York"
          label="Address"
          iconSrc="/assets/icons/user.svg"
          iconAlt="user"
        />

          <CustomFormField
          fieldType={FormFieldType.INPUT}
          control={form.control}
          name="occupation"
          placeholder="Software Engineer"
          label="Occupation"
          iconSrc="/assets/icons/user.svg"
          iconAlt="user"
        />

        
        </div>

        <div className="flex flex-col gap-6 xl:flex-row ">
               <CustomFormField
          fieldType={FormFieldType.INPUT}
          control={form.control}
          name="emergencyContactName"
          label="Emergency Contact Name"
          placeholder="Guardian's Name"
          
        />

        <CustomFormField
          fieldType={FormFieldType.PHONE_INPUT}
          control={form.control}
          name="emergencyContactNumber"
          label="Emergency Contact Number"
          placeholder="+216 12234456"
          
        />
        </div>

           <section className="space-y-6">
          <div className="mb-9 space-y-1">
             <h2 className="sub-header">Medical information</h2>
          </div>
        </section>

        <div className="flex flex-col gap-6 xl:flex-row ">
          <CustomFormField
          fieldType={FormFieldType.SELECT}
          control={form.control}
          name="primaryPhysician"
          label="primary Physician"
          placeholder="Select a primary physician"
          iconSrc="/assets/icons/email.svg"
          iconAlt="email"
        >
          {Doctors.map((doctor) => (
            <SelectItem key={doctor.name} value={doctor.name}>
              <div className="flex cursor-pointer items-center gap-2">
                 <Image src={doctor.image} width={32} height={32} alt={doctor.name} className="rounded-full border border-dark-500"/>
                  <p>{doctor.name}</p>

              </div>
             
            </SelectItem>
          ))}
        </CustomFormField>

        </div>

        <div className="flex flex-col gap-6 xl:flex-row ">

        </div>

        <div className="flex flex-col gap-6 xl:flex-row ">

        </div>

        <div className="flex flex-col gap-6 xl:flex-row ">

        </div>

        <div className="flex flex-col gap-6 xl:flex-row ">

        </div>
        
        <SubmitButton isLoading={isLoading}>Get Started</SubmitButton>
      </form>
    </Form>
  )
}

export default RegisterForm;
