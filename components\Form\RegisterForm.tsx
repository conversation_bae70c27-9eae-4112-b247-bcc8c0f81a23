"use client"



import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
 
import {
  Form,
  FormControl,
} from "@/components/ui/form"
import CustomFormField from "../CustomFormField"
import SubmitButton from "../SubmitButton"
import { useState } from "react"
import { UserFormValidation } from "@/lib/Validation"
import { useRouter } from "next/navigation"
import { createUser } from "@/lib/actions/patient.actions"
import { FormFieldType } from "./PatientForm"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { GenderOptions } from "@/constants"


const  RegisterForm = ({user}:{user:User})=> {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  // 1. Define your form.
  const form = useForm<z.infer<typeof UserFormValidation>>({
    resolver: zodResolver(UserFormValidation),
    defaultValues: {
      username: "",
      email: "",
      phone: "",
    },
  })

  // 2. Define a submit handler.
  async function onSubmit({username,email,phone}: z.infer<typeof UserFormValidation>) {
    setIsLoading(true);
    try {
      const userData = {username,email,phone}

      // Simulate API call
     const user =  await createUser(userData);
     if(user) router.push(`/patients/${user.$id}/register`)

    } catch (error) {
      console.error("Form submission error:", error);
    } finally {
      setIsLoading(false);
    }
  }
  return (
     <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-12 flex-1">
        <section className="space-y-4">
          <h1 className="header">Welcome👋</h1>
          <p className="text-dark-700">Let us know more about yourself.</p>
        </section>

        <section className="space-y-6">
          <div className="mb-9 space-y-1">
             <h2 className="sub-header">Personal information</h2>
          </div>
        </section>

        <CustomFormField
          fieldType={FormFieldType.INPUT}
          control={form.control}
          name="username"
          placeholder="John Doe"
          label="Full Name"
          iconSrc="/assets/icons/user.svg"
          iconAlt="user"
        />
        <div className="flex flex-col gap-6 xl:flex-row ">
          <CustomFormField
          fieldType={FormFieldType.INPUT}
          control={form.control}
          name="email"
          label="Email"
          placeholder="<EMAIL>"
          iconSrc="/assets/icons/email.svg"
          iconAlt="email"
        />

        <CustomFormField
          fieldType={FormFieldType.PHONE_INPUT}
          control={form.control}
          name="phone"
          label="Phone"
          placeholder="+216 12234456"
          iconSrc="/assets/icons/email.svg"
          iconAlt="email"
        />

        </div>

        <div className="flex flex-col gap-6 xl:flex-row ">
        <CustomFormField
          fieldType={FormFieldType.DATE_PICKER}
          control={form.control}
          name="birthDate"
          label="Date of Birth"
          placeholder="<EMAIL>"
          
        />

        <CustomFormField
          fieldType={FormFieldType.SKELETON}
          control={form.control}
          name="Gender"
          label="Gebder"
          renderSkeleton={(field)=>(
            <FormControl>
              <RadioGroup className="flex h-11 gap-6 xl:justify-between"onValueChange={field.onChange} defaultValue={field.value}></RadioGroup>
                {GenderOptions.map((option)=>(
                  <div key={option} className="radio-group">
                      <RadioGroupItem value={option} id={option} />
                      <Label htmlFor={option} className="cursor-pointer">
                        {option}
                      </Label>
                  </div>
                ))}
            </FormControl>
          )}
        />
        </div>

        <div className="flex flex-col gap-6 xl:flex-row ">
          
        </div>

        <div className="flex flex-col gap-6 xl:flex-row ">
          
        </div>
        
        <SubmitButton isLoading={isLoading}>Get Started</SubmitButton>
      </form>
    </Form>
  )
}

export default RegisterForm;
