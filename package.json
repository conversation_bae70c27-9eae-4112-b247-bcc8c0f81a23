{"name": "carepulse_healthcare", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "appwrite": "^18.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "next": "15.4.1", "next-themes": "^0.4.6", "node-appwrite": "^17.1.0", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.60.0", "react-phone-number-input": "^3.4.12", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.20", "eslint": "^9", "eslint-config-next": "15.4.1", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "typescript": "^5"}}